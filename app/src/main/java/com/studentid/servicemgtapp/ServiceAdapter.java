package com.studentid.servicemgtapp;

import android.app.AlertDialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class ServiceAdapter extends RecyclerView.Adapter<ServiceAdapter.ServiceViewHolder> {
    private List<Service> serviceList;
    private Context context;
    private OnServiceDeleteListener deleteListener;

    public interface OnServiceDeleteListener {
        void onServiceDelete(int position);
    }

    public ServiceAdapter(Context context, List<Service> serviceList) {
        this.context = context;
        this.serviceList = serviceList;
    }

    public void setOnServiceDeleteListener(OnServiceDeleteListener listener) {
        this.deleteListener = listener;
    }

    @NonNull
    @Override
    public ServiceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_service, parent, false);
        return new ServiceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ServiceViewHolder holder, int position) {
        Service service = serviceList.get(position);
        
        holder.tvRowNumber.setText(String.valueOf(position + 1));
        holder.tvServiceNo.setText(service.getServiceNo());
        holder.tvServiceName.setText(service.getServiceName());
        holder.tvType.setText(service.getType());
        holder.tvPrice.setText(String.valueOf((int)service.getPrice()));
        
        holder.btnDelete.setOnClickListener(v -> showDeleteConfirmDialog(position));
    }

    @Override
    public int getItemCount() {
        return serviceList.size();
    }

    private void showDeleteConfirmDialog(int position) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        
        // Create custom dialog view
        View dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_confirm_delete, null);
        builder.setView(dialogView);
        
        AlertDialog dialog = builder.create();
        
        Button btnYes = dialogView.findViewById(R.id.btn_yes);
        Button btnNo = dialogView.findViewById(R.id.btn_no);
        
        btnYes.setOnClickListener(v -> {
            if (deleteListener != null) {
                deleteListener.onServiceDelete(position);
            }
            dialog.dismiss();
        });
        
        btnNo.setOnClickListener(v -> dialog.dismiss());
        
        dialog.show();
    }

    public void updateData(List<Service> newServiceList) {
        this.serviceList = newServiceList;
        notifyDataSetChanged();
    }

    public static class ServiceViewHolder extends RecyclerView.ViewHolder {
        TextView tvRowNumber, tvServiceNo, tvServiceName, tvType, tvPrice;
        Button btnDelete;

        public ServiceViewHolder(@NonNull View itemView) {
            super(itemView);
            tvRowNumber = itemView.findViewById(R.id.tv_row_number);
            tvServiceNo = itemView.findViewById(R.id.tv_service_no);
            tvServiceName = itemView.findViewById(R.id.tv_service_name);
            tvType = itemView.findViewById(R.id.tv_type);
            tvPrice = itemView.findViewById(R.id.tv_price);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }
    }
}
