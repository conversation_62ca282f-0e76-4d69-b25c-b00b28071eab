package com.studentid.servicemgtapp;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;

public class MainActivity extends AppCompatActivity implements ServiceAdapter.OnServiceDeleteListener {
    private RecyclerView recyclerView;
    private ServiceAdapter adapter;
    private DatabaseHelper databaseHelper;
    private List<Service> serviceList;
    private Button btnCreateNewService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initViews();
        setupRecyclerView();
        loadServices();
        setupClickListeners();
    }

    private void initViews() {
        recyclerView = findViewById(R.id.recycler_view_services);
        btnCreateNewService = findViewById(R.id.btn_create_new_service);
        databaseHelper = new DatabaseHelper(this);
    }

    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
    }

    private void loadServices() {
        serviceList = databaseHelper.getAllServices();
        adapter = new ServiceAdapter(this, serviceList);
        adapter.setOnServiceDeleteListener(this);
        recyclerView.setAdapter(adapter);
    }

    private void setupClickListeners() {
        btnCreateNewService.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, CreateServiceActivity.class);
            startActivityForResult(intent, 1);
        });
    }

    @Override
    public void onServiceDelete(int position) {
        Service service = serviceList.get(position);
        databaseHelper.deleteService(service.getId());
        serviceList.remove(position);
        adapter.notifyItemRemoved(position);
        adapter.notifyItemRangeChanged(position, serviceList.size());
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1 && resultCode == RESULT_OK) {
            // Refresh the list when returning from CreateServiceActivity
            refreshServiceList();
        }
    }

    private void refreshServiceList() {
        serviceList = databaseHelper.getAllServices();
        adapter.updateData(serviceList);
    }

    @Override
    protected void onResume() {
        super.onResume();
        refreshServiceList();
    }
}
