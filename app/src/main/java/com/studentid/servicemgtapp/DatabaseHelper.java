package com.studentid.servicemgtapp;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import java.util.ArrayList;
import java.util.List;

public class DatabaseHelper extends SQLiteOpenHelper {
    private static final String DATABASE_NAME = "ServiceDatabase";
    private static final int DATABASE_VERSION = 1;
    
    private static final String TABLE_SERVICES = "services";
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_SERVICE_NO = "service_no";
    private static final String COLUMN_SERVICE_NAME = "service_name";
    private static final String COLUMN_TYPE = "type";
    private static final String COLUMN_PRICE = "price";

    private static final String CREATE_TABLE_SERVICES = "CREATE TABLE " + TABLE_SERVICES + "("
            + COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT,"
            + COLUMN_SERVICE_NO + " TEXT NOT NULL,"
            + COLUMN_SERVICE_NAME + " TEXT NOT NULL,"
            + COLUMN_TYPE + " TEXT NOT NULL,"
            + COLUMN_PRICE + " REAL NOT NULL" + ")";

    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(CREATE_TABLE_SERVICES);
        
        // Insert sample data
        insertSampleData(db);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_SERVICES);
        onCreate(db);
    }

    private void insertSampleData(SQLiteDatabase db) {
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_SERVICE_NO, "ABS-23.341");
        values.put(COLUMN_SERVICE_NAME, "House clean");
        values.put(COLUMN_TYPE, "Housing");
        values.put(COLUMN_PRICE, 123);
        db.insert(TABLE_SERVICES, null, values);

        values.clear();
        values.put(COLUMN_SERVICE_NO, "SCC-23.341");
        values.put(COLUMN_SERVICE_NAME, "Skin care");
        values.put(COLUMN_TYPE, "Health care");
        values.put(COLUMN_PRICE, 300);
        db.insert(TABLE_SERVICES, null, values);

        values.clear();
        values.put(COLUMN_SERVICE_NO, "BBW-23.341");
        values.put(COLUMN_SERVICE_NAME, "Baby wash");
        values.put(COLUMN_TYPE, "Baby care");
        values.put(COLUMN_PRICE, 400);
        db.insert(TABLE_SERVICES, null, values);
    }

    public long addService(Service service) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_SERVICE_NO, service.getServiceNo());
        values.put(COLUMN_SERVICE_NAME, service.getServiceName());
        values.put(COLUMN_TYPE, service.getType());
        values.put(COLUMN_PRICE, service.getPrice());
        
        long id = db.insert(TABLE_SERVICES, null, values);
        db.close();
        return id;
    }

    public List<Service> getAllServices() {
        List<Service> serviceList = new ArrayList<>();
        String selectQuery = "SELECT * FROM " + TABLE_SERVICES;
        
        SQLiteDatabase db = this.getWritableDatabase();
        Cursor cursor = db.rawQuery(selectQuery, null);
        
        if (cursor.moveToFirst()) {
            do {
                Service service = new Service();
                service.setId(cursor.getInt(0));
                service.setServiceNo(cursor.getString(1));
                service.setServiceName(cursor.getString(2));
                service.setType(cursor.getString(3));
                service.setPrice(cursor.getDouble(4));
                serviceList.add(service);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        db.close();
        return serviceList;
    }

    public void deleteService(int id) {
        SQLiteDatabase db = this.getWritableDatabase();
        db.delete(TABLE_SERVICES, COLUMN_ID + " = ?", new String[]{String.valueOf(id)});
        db.close();
    }

    public Service getService(int id) {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.query(TABLE_SERVICES, new String[]{COLUMN_ID, COLUMN_SERVICE_NO, 
                COLUMN_SERVICE_NAME, COLUMN_TYPE, COLUMN_PRICE}, COLUMN_ID + "=?",
                new String[]{String.valueOf(id)}, null, null, null, null);
        
        if (cursor != null)
            cursor.moveToFirst();
        
        Service service = new Service(cursor.getInt(0), cursor.getString(1), 
                cursor.getString(2), cursor.getString(3), cursor.getDouble(4));
        cursor.close();
        db.close();
        return service;
    }

    public int updateService(Service service) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_SERVICE_NO, service.getServiceNo());
        values.put(COLUMN_SERVICE_NAME, service.getServiceName());
        values.put(COLUMN_TYPE, service.getType());
        values.put(COLUMN_PRICE, service.getPrice());
        
        int result = db.update(TABLE_SERVICES, values, COLUMN_ID + " = ?",
                new String[]{String.valueOf(service.getId())});
        db.close();
        return result;
    }
}
