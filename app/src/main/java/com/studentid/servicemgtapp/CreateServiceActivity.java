package com.studentid.servicemgtapp;

import android.os.Bundle;
import android.text.TextUtils;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

public class CreateServiceActivity extends AppCompatActivity {
    private EditText etServiceNo, etServiceName, etPrice;
    private Spinner spinnerType;
    private Button btnSave, btnBackToServiceList;
    private DatabaseHelper databaseHelper;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_create_service);

        initViews();
        setupSpinner();
        setupClickListeners();
    }

    private void initViews() {
        etServiceNo = findViewById(R.id.et_service_no);
        etServiceName = findViewById(R.id.et_service_name);
        etPrice = findViewById(R.id.et_price);
        spinnerType = findViewById(R.id.spinner_type);
        btnSave = findViewById(R.id.btn_save);
        btnBackToServiceList = findViewById(R.id.btn_back_to_service_list);
        databaseHelper = new DatabaseHelper(this);
    }

    private void setupSpinner() {
        String[] types = {"Housing", "Health care", "Baby care"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, types);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerType.setAdapter(adapter);
    }

    private void setupClickListeners() {
        btnSave.setOnClickListener(v -> saveService());
        btnBackToServiceList.setOnClickListener(v -> finish());
    }

    private void saveService() {
        String serviceNo = etServiceNo.getText().toString().trim();
        String serviceName = etServiceName.getText().toString().trim();
        String type = spinnerType.getSelectedItem().toString();
        String priceStr = etPrice.getText().toString().trim();

        // Validation
        if (TextUtils.isEmpty(serviceNo)) {
            etServiceNo.setError("Service No is required");
            etServiceNo.requestFocus();
            return;
        }

        if (TextUtils.isEmpty(serviceName)) {
            etServiceName.setError("Service Name is required");
            etServiceName.requestFocus();
            return;
        }

        if (TextUtils.isEmpty(priceStr)) {
            etPrice.setError("Price is required");
            etPrice.requestFocus();
            return;
        }

        double price;
        try {
            price = Double.parseDouble(priceStr);
            if (price < 0) {
                etPrice.setError("Price must be positive");
                etPrice.requestFocus();
                return;
            }
        } catch (NumberFormatException e) {
            etPrice.setError("Invalid price format");
            etPrice.requestFocus();
            return;
        }

        // Create and save service
        Service service = new Service(serviceNo, serviceName, type, price);
        long result = databaseHelper.addService(service);

        if (result != -1) {
            Toast.makeText(this, "Service saved successfully", Toast.LENGTH_SHORT).show();
            setResult(RESULT_OK);
            finish();
        } else {
            Toast.makeText(this, "Error saving service", Toast.LENGTH_SHORT).show();
        }
    }
}
