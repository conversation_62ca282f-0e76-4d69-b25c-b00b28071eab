<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Service List"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Table Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#e0e0e0"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:text="#"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.5"
            android:text="Service No"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:text="Service Name"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.5"
            android:text="Type"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Price ($)"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Action"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center" />

    </LinearLayout>

    <!-- RecyclerView for Services -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_services"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#ffffff"
        android:layout_marginTop="1dp" />

    <!-- Create New Service Button -->
    <Button
        android:id="@+id/btn_create_new_service"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Create New Service"
        android:textColor="#ffffff"
        android:background="#4CAF50"
        android:layout_marginTop="16dp"
        android:padding="12dp"
        android:textSize="16sp" />

</LinearLayout>
