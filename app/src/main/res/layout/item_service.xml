<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:background="#ffffff">

    <TextView
        android:id="@+id/tv_row_number"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.5"
        android:text="1"
        android:textColor="#333333"
        android:gravity="center"
        android:padding="4dp" />

    <TextView
        android:id="@+id/tv_service_no"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.5"
        android:text="ABS-23.341"
        android:textColor="#333333"
        android:gravity="center"
        android:padding="4dp" />

    <TextView
        android:id="@+id/tv_service_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:text="House clean"
        android:textColor="#333333"
        android:gravity="center"
        android:padding="4dp" />

    <TextView
        android:id="@+id/tv_type"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.5"
        android:text="Housing"
        android:textColor="#333333"
        android:gravity="center"
        android:padding="4dp" />

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="123"
        android:textColor="#333333"
        android:gravity="center"
        android:padding="4dp" />

    <Button
        android:id="@+id/btn_delete"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Delete"
        android:textColor="#ffffff"
        android:background="#f44336"
        android:textSize="12sp"
        android:padding="4dp" />

</LinearLayout>
