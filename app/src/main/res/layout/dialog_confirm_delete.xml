<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="#ffffff">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Confirmation"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Message -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Are you sure you want to delete?"
        android:textSize="16sp"
        android:textColor="#666666"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btn_yes"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Yes"
            android:textColor="#ffffff"
            android:background="#4CAF50"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_no"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="No"
            android:textColor="#333333"
            android:background="#e0e0e0"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
