<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5">

    <!-- Back Button -->
    <Button
        android:id="@+id/btn_back_to_service_list"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="← Back to Service List"
        android:textColor="#333333"
        android:background="#e0e0e0"
        android:layout_marginBottom="16dp" />

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Create New Service"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Form Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#ffffff"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <!-- Service No -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Service No"
            android:textColor="#333333"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <EditText
            android:id="@+id/et_service_no"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter service number"
            android:padding="12dp"
            android:background="#f0f0f0"
            android:layout_marginBottom="16dp" />

        <!-- Service Name -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Service Name"
            android:textColor="#333333"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <EditText
            android:id="@+id/et_service_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter service name"
            android:padding="12dp"
            android:background="#f0f0f0"
            android:layout_marginBottom="16dp" />

        <!-- Type -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Type"
            android:textColor="#333333"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <Spinner
            android:id="@+id/spinner_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp"
            android:background="#f0f0f0"
            android:layout_marginBottom="16dp" />

        <!-- Price -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Price ($)"
            android:textColor="#333333"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <EditText
            android:id="@+id/et_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter price"
            android:inputType="numberDecimal"
            android:padding="12dp"
            android:background="#f0f0f0"
            android:layout_marginBottom="16dp" />

    </LinearLayout>

    <!-- Save Button -->
    <Button
        android:id="@+id/btn_save"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Save"
        android:textColor="#ffffff"
        android:background="#4CAF50"
        android:padding="12dp"
        android:textSize="16sp" />

</LinearLayout>
